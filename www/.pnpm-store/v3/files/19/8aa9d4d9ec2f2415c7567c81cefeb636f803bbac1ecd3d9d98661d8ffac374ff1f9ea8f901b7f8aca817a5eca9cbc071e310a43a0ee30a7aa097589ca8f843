'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M3.75 3.75a1 1 0 0 0 0 2h16.5a1 1 0 1 0 0-2H3.75zm12.124 5.5a4.267 4.267 0 0 0-1.872-2A4.231 4.231 0 0 0 12 6.75c-.724 0-1.405.18-2.002.5a4.267 4.267 0 0 0-1.872 2A4.227 4.227 0 0 0 7.75 11v4.75a1 1 0 1 0 2 0V11A2.247 2.247 0 0 1 12 8.75A2.247 2.247 0 0 1 14.25 11v4.75a1 1 0 1 0 2 0V11a4.227 4.227 0 0 0-.376-1.75zm-12.124-2h4.576a5.253 5.253 0 0 0-1.277 2H3.75a1 1 0 0 1 0-2zm0 3.5h3.006a5.329 5.329 0 0 0-.006.25v1.75h-3a1 1 0 1 1 0-2zm0 3.5h3v1.5c0 .173.022.34.063.5H3.75a1 1 0 1 1 0-2zm7 1.5c0 .173-.022.34-.063.5h2.626a2.005 2.005 0 0 1-.063-.5v-1.5h-2.5v1.5zm6.5 0c0 .173-.022.34-.063.5h3.063a1 1 0 1 0 0-2h-3v1.5zm3-3h-3V11c0-.084-.002-.167-.006-.25h3.006a1 1 0 1 1 0 2zm-7-1.75v1.75h-2.5V11c0-.086.009-.17.025-.25h2.45c.016.08.025.164.025.25zm7-1.75h-3.299a5.252 5.252 0 0 0-1.277-2h4.576a1 1 0 1 1 0 2zm-16.5 8.5h16.5a1 1 0 1 1 0 2H3.75a1 1 0 1 1 0-2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TextPositionThrough24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
