import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M12 2v6a2 2 0 0 0 2 2h6v10a2 2 0 0 1-2 2H9.49l3.17-3.17a4.467 4.467 0 0 0-5.645-6.87A4.466 4.466 0 0 0 4 11.235V4a2 2 0 0 1 2-2h6zm1.5.5V8a.5.5 0 0 0 .5.5h5.5l-6-6zM7.015 22.75a.747.747 0 0 1-.53-.22l-4.41-4.408a3.467 3.467 0 1 1 4.904-4.903l.036.036l.036-.036a3.467 3.467 0 1 1 4.902 4.903L7.545 22.53a.747.747 0 0 1-.53.22z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DocumentHeart24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
