import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 448 512'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M448 48v336c-63 23-82 32-119 32c-63 0-87-32-150-32c-20 0-36 4-51 8v-64c15-4 31-8 51-8c63 0 87 32 150 32c20 0 35-3 55-9V135c-20 6-35 9-55 9c-63 0-87-32-150-32c-51 0-75 21-115 29v307a31.6 31.6 0 0 1-32 32a31.6 31.6 0 0 1-32-32V64a31.6 31.6 0 0 1 32-32a31.6 31.6 0 0 1 32 32v13c40-8 64-29 115-29c63 0 87 32 150 32c37 0 56-9 119-32z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'FontAwesomeFlag',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
