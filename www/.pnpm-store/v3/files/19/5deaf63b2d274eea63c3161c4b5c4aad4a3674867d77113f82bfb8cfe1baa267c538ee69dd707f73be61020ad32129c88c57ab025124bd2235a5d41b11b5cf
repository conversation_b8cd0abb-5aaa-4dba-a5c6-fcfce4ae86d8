'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M17.06 9c.833 0 1.64.277 2.295.784l.175.144l2.586 2.263c.19.166.425.27.673.3l.15.009H40.25a3.75 3.75 0 0 1 3.745 3.55l.005.2v19a3.75 3.75 0 0 1-3.55 3.745l-.2.005H7.75a3.75 3.75 0 0 1-3.745-3.55L4 35.25v-22.5a3.75 3.75 0 0 1 3.55-3.745L7.75 9h9.31zm5.787 5.999l-2.127 2.616a3.75 3.75 0 0 1-2.685 1.378L17.81 19L6.5 18.999V35.25c0 .647.492 1.18 1.122 1.243l.128.007h32.5a1.25 1.25 0 0 0 1.243-1.122l.007-.128v-19a1.25 1.25 0 0 0-1.122-1.243L40.25 15l-17.403-.001zM17.061 11.5H7.75a1.25 1.25 0 0 0-1.244 1.122l-.006.128v3.749l11.31.001c.33 0 .643-.13.876-.358l.094-.104l1.635-2.013l-2.531-2.216a1.25 1.25 0 0 0-.673-.3l-.15-.009z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Folder48Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
