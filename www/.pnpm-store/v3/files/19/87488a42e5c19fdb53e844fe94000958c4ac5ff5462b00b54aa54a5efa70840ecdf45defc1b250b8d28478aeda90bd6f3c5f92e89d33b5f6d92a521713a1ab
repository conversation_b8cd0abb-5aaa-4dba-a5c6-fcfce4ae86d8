'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M7.738 16.445A2.986 2.986 0 0 1 6 17a3 3 0 0 1-2.445-4.738l4.183 4.183zm5.902-5.358l-2.23 1.152a.75.75 0 0 0-.41.664v2.194a.75.75 0 0 0 .41.664l2.23 1.152a.786.786 0 0 0 .72 0l2.23-1.152a.75.75 0 0 0 .41-.664v-2.194a.75.75 0 0 0-.41-.664l-2.23-1.152a.786.786 0 0 0-.72 0zm-5.195 4.651A2.987 2.987 0 0 0 9 14a3 3 0 0 0-4.738-2.445l4.183 4.183zM13 3a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2h-2zm-8.087.674a1.214 1.214 0 0 1 2.174 0l1.78 3.537C9.277 8.03 8.688 9 7.778 9H4.22c-.91 0-1.5-.97-1.087-1.79l1.779-3.536z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Diversity20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
