'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M4.07 13H2.05c.2 2.01 1 3.84 2.21 5.33l1.43-1.43A7.941 7.941 0 0 1 4.07 13zm1.62-5.9L4.26 5.67A10.013 10.013 0 0 0 2.05 11h2.02c.18-1.46.76-2.79 1.62-3.9zM11 4.07V2.05c-2.01.2-3.84 1-5.33 2.21L7.1 5.69A7.941 7.941 0 0 1 11 4.07zm7.33.19A10.013 10.013 0 0 0 13 2.05v2.02c1.46.18 2.79.76 3.9 1.62l1.43-1.43zm-.02 12.64l1.43 1.43A9.98 9.98 0 0 0 21.95 13h-2.02a7.941 7.941 0 0 1-1.62 3.9zm1.62-5.9h2.02c-.2-2.01-1-3.84-2.21-5.33L18.31 7.1a7.941 7.941 0 0 1 1.62 3.9zM13 19.93v2.02c2.01-.2 3.84-1 5.33-2.21l-1.43-1.43a7.941 7.941 0 0 1-3.9 1.62zm-7.33-.19A9.98 9.98 0 0 0 11 21.95v-2.02a7.941 7.941 0 0 1-3.9-1.62l-1.43 1.43z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'circle',
  {
    cx: '12',
    cy: '12',
    r: '5',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = [_hoisted_2, _hoisted_3]
exports.default = (0, vue_1.defineComponent)({
  name: 'NoiseAwareSharp',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_4)
  }
})
