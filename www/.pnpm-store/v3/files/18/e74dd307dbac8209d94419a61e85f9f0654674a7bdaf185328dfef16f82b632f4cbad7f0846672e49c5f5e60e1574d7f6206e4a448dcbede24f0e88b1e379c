import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M7.5 2a3.5 3.5 0 1 1 0 7a3.5 3.5 0 0 1 0-7zm7 7a2.5 2.5 0 1 0 0-5a2.5 2.5 0 0 0 0 5zm-3.195 1.023A2.025 2.025 0 0 0 11 10H4a2 2 0 0 0-2 2v1.5C2 15.554 4.088 17 7.5 17c.732 0 1.404-.067 2.006-.192A5.48 5.48 0 0 1 9 14.5c0-1.846.91-3.48 2.305-4.477zM10 14.5a4.5 4.5 0 1 0 9 0a4.5 4.5 0 0 0-9 0zm2.404 2.803l4.9-4.9a3.5 3.5 0 0 1-4.9 4.9zm-.707-.707a3.5 3.5 0 0 1 4.9-4.9l-4.9 4.9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'PeopleProhibited20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
