import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M12.5 3.5c0-1.1.9-2 2-2s2 .9 2 2s-.9 2-2 2s-2-.9-2-2zM6.32 19.03l-1.14-1.47L4 18.5l2.38 3.04c.51.65 1.16 1.15 1.88 1.41c.28.1.53.04.72-.11c.3-.23.42-.7.12-1.07a.84.84 0 0 0-.31-.22a2.97 2.97 0 0 1-1.14-.83l-.08-.1L11 18.2l.89-3.22l2.11 2v4.52h-2V23h3.87c.82 0 1.61-.21 2.26-.61c.26-.16.37-.39.37-.64c0-.38-.3-.75-.77-.75c-.13 0-.26.04-.37.1c-.4.23-.87.37-1.36.4v-6.02l-2.11-2l.6-3C15.79 11.98 17.8 13 20 13v-2c-1.9 0-3.51-1.02-4.31-2.42l-1-1.58c-.4-.6-1-1-1.7-1c-.75 0-1.41.34-5.99 2.28V13h2V9.58l1.79-.7L9.2 17l-2.88 2.03z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'SnowshoeingOutlined',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
