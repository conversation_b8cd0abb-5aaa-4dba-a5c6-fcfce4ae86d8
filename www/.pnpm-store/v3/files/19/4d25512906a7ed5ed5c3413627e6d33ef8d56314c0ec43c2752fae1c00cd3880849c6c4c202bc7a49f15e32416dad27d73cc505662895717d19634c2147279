'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M5.55 5.3c0-.412.218-.833.675-1.175c.458-.344 1.122-.575 1.875-.575c1.116 0 2.036.523 2.364 1.047a.75.75 0 1 0 1.272-.795C11.063 2.727 9.584 2.05 8.1 2.05c-1.047 0-2.034.319-2.775.875C4.582 3.482 4.05 4.312 4.05 5.3c0 .532.155 1.022.423 1.45H6.69c-.774-.364-1.14-.951-1.14-1.45zM2 8.75A.75.75 0 0 1 2.75 8h10.5a.75.75 0 0 1 0 1.5h-1.635c.213.391.335.828.335 1.3c0 .988-.532 1.817-1.275 2.375c-.742.556-1.728.875-2.775.875c-1.489 0-2.987-.683-3.656-1.886a.75.75 0 1 1 1.311-.728c.332.596 1.234 1.114 2.345 1.114c.753 0 1.416-.231 1.875-.575c.457-.343.675-.763.675-1.175c0-.435-.279-.937-.863-1.3H2.75A.75.75 0 0 1 2 8.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TextStrikethrough16Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
