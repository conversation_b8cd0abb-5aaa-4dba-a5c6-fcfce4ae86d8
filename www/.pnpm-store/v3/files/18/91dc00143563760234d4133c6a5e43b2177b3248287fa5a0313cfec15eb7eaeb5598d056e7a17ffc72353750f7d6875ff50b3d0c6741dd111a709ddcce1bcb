'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M9.75 10A2.75 2.75 0 0 0 7 12.75v3.5A2.75 2.75 0 0 0 9.75 19h4.5A2.75 2.75 0 0 0 17 16.25v-3.5A2.75 2.75 0 0 0 14.25 10h-4.5zM8.5 12.75c0-.69.56-1.25 1.25-1.25H12V14H8.5v-1.25zm0 2.75H12v2H9.75c-.69 0-1.25-.56-1.25-1.25v-.75zm5 2v-6h.75c.69 0 1.25.56 1.25 1.25v3.5c0 .69-.56 1.25-1.25 1.25h-.75zM7.25 2A3.25 3.25 0 0 0 4 5.25v13.5A3.25 3.25 0 0 0 7.25 22h9.5A3.25 3.25 0 0 0 20 18.75V9.286a3.25 3.25 0 0 0-.952-2.299l-4.035-4.035A3.25 3.25 0 0 0 12.714 2H7.25zM5.5 5.25c0-.966.784-1.75 1.75-1.75h5.464c.465 0 .91.184 1.238.513l4.035 4.035c.329.328.513.773.513 1.238v9.464a1.75 1.75 0 0 1-1.75 1.75h-9.5a1.75 1.75 0 0 1-1.75-1.75V5.25z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Sim24Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
