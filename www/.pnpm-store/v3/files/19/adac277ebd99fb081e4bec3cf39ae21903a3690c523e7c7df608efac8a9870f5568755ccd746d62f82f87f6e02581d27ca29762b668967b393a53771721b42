import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M14.374 2.334A.75.75 0 0 0 13 2.75v5.5a.75.75 0 0 0 1.5 0V5.227l1.313 1.969c.2.3.583.415.915.275l3.067-1.293l-1.23 2.767A.75.75 0 0 0 19.25 10h3.399l-1.475 1.77a.75.75 0 0 0 .012.974l2.411 2.756h-2.845a.75.75 0 1 0 0 1.5h4.498a.75.75 0 0 0 .564-1.244l-3.078-3.518l2.09-2.508a.75.75 0 0 0-.576-1.23h-3.846l1.531-3.445a.75.75 0 0 0-.976-.996l-4.242 1.789l-2.343-3.514zM14.5 20a1 1 0 1 0 0-2a1 1 0 0 0 0 2zM7 19a1 1 0 1 1-2 0a1 1 0 0 1 2 0zm1.75 0a.75.75 0 1 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3zm-6-3.5h.418A2.747 2.747 0 0 0 2 17.75v4.5c0 .043.004.084.01.125A.755.755 0 0 0 2 22.5v1.75c0 .967.784 1.75 1.75 1.75h1.5A1.75 1.75 0 0 0 7 24.25V23h6.5v1.25c0 .967.784 1.75 1.75 1.75h1.5a1.75 1.75 0 0 0 1.75-1.75v-6.5c0-.93-.462-1.752-1.169-2.25h.419a.75.75 0 0 0 0-1.5H16.5a.755.755 0 0 0-.144.014l-.32-1.756A2.75 2.75 0 0 0 13.331 10H7.169a2.75 2.75 0 0 0-2.706 2.258L4.147 14H2.75a.75.75 0 1 0 0 1.5zm.75 2.25c0-.69.56-1.25 1.25-1.25h11c.69 0 1.25.56 1.25 1.25v3.75H3.5v-3.75zm11.06-5.224L15.01 15H5.49l.45-2.474A1.25 1.25 0 0 1 7.17 11.5h6.16a1.25 1.25 0 0 1 1.23 1.026zM15 24.25V23h2v1.25a.25.25 0 0 1-.25.25h-1.5a.25.25 0 0 1-.25-.25zM5.5 23v1.25a.25.25 0 0 1-.25.25h-1.5a.25.25 0 0 1-.25-.25V23h2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'VehicleCarCollision28Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
