import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.75 2A1.75 1.75 0 0 0 7 3.75v3a.25.25 0 0 1-.25.25h-3A1.75 1.75 0 0 0 2 8.75v3c0 .966.784 1.75 1.75 1.75h8a1.75 1.75 0 0 0 1.75-1.75v-3a.25.25 0 0 1 .25-.25h2.5A1.75 1.75 0 0 0 18 6.75v-3A1.75 1.75 0 0 0 16.25 2h-7.5zm7.5 5H13.5V3.5h2.75a.25.25 0 0 1 .25.25v3a.25.25 0 0 1-.25.25zM12 7H8.483c.011-.082.017-.165.017-.25v-3a.25.25 0 0 1 .25-.25H12V7zM7 8.5V12H3.75a.25.25 0 0 1-.25-.25v-3a.25.25 0 0 1 .25-.25H7zm1.5 0h3.518a1.762 1.762 0 0 0-.018.25v3a.25.25 0 0 1-.25.25H8.5V8.5zm8.75 2a1.75 1.75 0 0 0-1.75 1.75v3a.25.25 0 0 1-.25.25h-8.5A1.75 1.75 0 0 0 5 17.25v3c0 .966.783 1.75 1.75 1.75h13.5A1.75 1.75 0 0 0 22 20.25v-8a1.75 1.75 0 0 0-1.75-1.75h-3zM17 12.25a.25.25 0 0 1 .25-.25h3a.25.25 0 0 1 .25.25v3.25h-3.518c.012-.082.018-.165.018-.25v-3zM17 17h3.5v3.25a.25.25 0 0 1-.25.25H17V17zm-1.5-.018V20.5h-4V17h3.75c.085 0 .168-.006.25-.018zM10 17v3.5H6.75a.25.25 0 0 1-.25-.25v-3a.25.25 0 0 1 .25-.25H10z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TetrisApp24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
