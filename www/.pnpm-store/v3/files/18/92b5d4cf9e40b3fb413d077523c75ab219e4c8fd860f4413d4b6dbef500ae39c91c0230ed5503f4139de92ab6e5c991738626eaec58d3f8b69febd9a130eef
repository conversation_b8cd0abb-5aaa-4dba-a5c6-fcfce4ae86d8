'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M15.253 1.998c.966 0 1.75.784 1.75 1.75v16.503a1.75 1.75 0 0 1-1.75 1.75H8.75A1.75 1.75 0 0 1 7 20.251V3.748c0-.966.784-1.75 1.75-1.75h6.503zM12.25 8H8.5v1.5h3.75a.75.75 0 0 0 0-1.5zm-1.923 3.248H8.5v1.5h1.827a.75.75 0 1 0 0-1.5zM12.25 14.5H8.5V16h3.75a.75.75 0 0 0 0-1.5zm-1.923 3H8.5V19h1.827a.75.75 0 0 0 0-1.5zm0-12.502H8.5v1.5h1.827a.75.75 0 0 0 0-1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Ruler24Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
