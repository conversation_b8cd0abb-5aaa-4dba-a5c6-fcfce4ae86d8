import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M3.28 2.22a.75.75 0 1 0-1.06 1.06l.409.41C2.239 4.093 2 4.643 2 5.25v10.502l.005.154a2.25 2.25 0 0 0 2.245 2.096h4.249V20.5H6.75l-.102.007A.75.75 0 0 0 6.75 22h8.931a1.749 1.749 0 0 1-.708-.464L14 20.496v.004h-4l-.001-2.498H11v-1.5H4.25l-.102-.007a.75.75 0 0 1-.648-.743V5.25l.007-.102a.747.747 0 0 1 .183-.397l10.833 10.833l-.363.416h-1.41a.75.75 0 0 0-.75.75v1.5c0 .414.336.75.75.75h1.41l1.526 1.744A.75.75 0 0 0 17 20.25v-2.189l3.72 3.72a.75.75 0 0 0 1.06-1.061L3.28 2.22zm17.216 15.094l-2.411-2.41a.75.75 0 0 1 1.115-.253c.189.14.435.387.674.745c.361.542.589 1.184.622 1.918zm2.192 2.193l-1.243-1.243c.036-.244.055-.498.055-.764c0-.952-.242-1.758-.643-2.427c-.234-.39-.46-.641-.587-.747a.75.75 0 1 1 .96-1.152c.248.207.585.58.913 1.128c.536.893.857 1.962.857 3.198a6.39 6.39 0 0 1-.312 2.007zM7.682 4.5L6.182 3h13.567a2.25 2.25 0 0 1 2.245 2.095l.005.155v7.268a3.344 3.344 0 0 0-.129-.112a1.745 1.745 0 0 0-1.371-.388V5.25a.75.75 0 0 0-.648-.744l-.102-.006H7.682z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DesktopSpeakerOff24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
