import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.928 10.556a.5.5 0 1 0-.99-.145l-.086.587h-1.24l.066-.442a.5.5 0 1 0-.99-.145l-.086.587H7a.5.5 0 1 0 0 1h.456l-.22 1.5H6.5a.5.5 0 1 0 0 1h.59l-.137.93a.5.5 0 1 0 .99.144l.157-1.074h1.24l-.137.93a.5.5 0 0 0 .99.144l.157-1.074H11a.5.5 0 0 0 0-1h-.503l.22-1.5h.783a.5.5 0 0 0 0-1h-.637l.065-.442zm-2.681 2.942l.22-1.5h1.239l-.22 1.5h-1.24zM14 18a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8zm0-1H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'DocumentPageBottomLeft20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
