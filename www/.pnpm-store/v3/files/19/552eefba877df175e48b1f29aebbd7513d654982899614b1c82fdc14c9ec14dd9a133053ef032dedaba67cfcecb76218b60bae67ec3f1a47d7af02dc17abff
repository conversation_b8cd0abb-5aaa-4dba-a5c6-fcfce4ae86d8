'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4.31 8l2.256 2.256a.75.75 0 0 1-.977 1.133l-.084-.073l-3.536-3.535l-.068-.08l-.04-.058l-.05-.095l-.033-.092l-.015-.067l-.01-.062l-.003-.094l.004-.059l.015-.094l.03-.1l.047-.098l.035-.055a.747.747 0 0 1 .088-.107l-.068.079a.753.753 0 0 1 .068-.079l3.536-3.535a.75.75 0 0 1 1.133.976l-.072.084L4.31 6.5H8c3.651 0 6.132 2.077 6.246 5.037l.004.213a.75.75 0 0 1-1.5 0c0-2.128-1.692-3.658-4.493-3.746L8 8H4.31l2.256 2.256L4.31 8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ArrowReply16Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
