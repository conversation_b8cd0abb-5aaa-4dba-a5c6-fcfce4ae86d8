'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<path d="M12 3v18a9 9 0 0 0 0-18z" fill="currentColor"></path><circle cx="6" cy="14" r="1" fill="currentColor"></circle><circle cx="6" cy="18" r="1" fill="currentColor"></circle><circle cx="6" cy="10" r="1" fill="currentColor"></circle><circle cx="3" cy="10" r=".5" fill="currentColor"></circle><circle cx="6" cy="6" r="1" fill="currentColor"></circle><circle cx="3" cy="14" r=".5" fill="currentColor"></circle><circle cx="10" cy="21" r=".5" fill="currentColor"></circle><circle cx="10" cy="3" r=".5" fill="currentColor"></circle><circle cx="10" cy="6" r="1" fill="currentColor"></circle><circle cx="10" cy="14" r="1.5" fill="currentColor"></circle><circle cx="10" cy="10" r="1.5" fill="currentColor"></circle><circle cx="10" cy="18" r="1" fill="currentColor"></circle>', 13)
const _hoisted_15 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DeblurRound',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_15)
  }
})
