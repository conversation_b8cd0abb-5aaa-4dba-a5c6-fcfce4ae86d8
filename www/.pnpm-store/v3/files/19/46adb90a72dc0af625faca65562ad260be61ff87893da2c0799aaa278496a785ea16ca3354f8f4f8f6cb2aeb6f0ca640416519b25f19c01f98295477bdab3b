'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M11.293 12l2.853 2.854a.5.5 0 0 0 .708-.708l-13-13a.5.5 0 1 0-.708.708l2.765 2.764A4.483 4.483 0 0 0 3.5 6.5v2.401l-.964 2.414A.5.5 0 0 0 3 12h3c0 1.108.892 2 2 2s2-.892 2-2h1.293zm-1-1H3.738l.726-1.817a.5.5 0 0 0 .036-.185V6.5c0-.389.063-.763.18-1.112L10.294 11zM8 13c-.556 0-1-.444-1-1h2c0 .556-.444 1-1 1zm3.69-3.432l-.154-.385a.5.5 0 0 1-.036-.185V6.5a3.5 3.5 0 0 0-5.645-2.766l-.711-.711A4.5 4.5 0 0 1 12.5 6.5v2.4l.964 2.414c.006.014.011.029.015.043l-1.79-1.79z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'AlertOff16Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
