'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 12 12'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M2 3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v1.563A2.004 2.004 0 0 0 7.563 6H4.437A2 2 0 0 0 2 4.563V3zm.5 2A1.5 1.5 0 0 0 1 6.5V9a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V6.5a1.5 1.5 0 0 0-3 0H4A1.5 1.5 0 0 0 2.5 5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Couch12Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
