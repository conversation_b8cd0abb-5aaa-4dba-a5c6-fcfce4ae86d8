'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M28 14.005h-1v-12h-2v12h-1a2.002 2.002 0 0 0-2 2v2a2.002 2.002 0 0 0 2 2h1v10h2v-10h1a2.003 2.003 0 0 0 2-2v-2a2.002 2.002 0 0 0-2-2zm0 4h-4v-2h4z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M18 6.005h-1v-4h-2v4h-1a2.002 2.002 0 0 0-2 2v2a2.002 2.002 0 0 0 2 2h1v18h2v-18h1a2.002 2.002 0 0 0 2-2v-2a2.002 2.002 0 0 0-2-2zm0 4h-4v-2h4z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M8 20.005H7v-18H5v18H4a2.002 2.002 0 0 0-2 2v2a2.002 2.002 0 0 0 2 2h1v4h2v-4h1a2.002 2.002 0 0 0 2-2v-2a2.002 2.002 0 0 0-2-2zm0 4H4v-2h4z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_5 = [_hoisted_2, _hoisted_3, _hoisted_4]
exports.default = (0, vue_1.defineComponent)({
  name: 'AudioConsole',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_5)
  }
})
