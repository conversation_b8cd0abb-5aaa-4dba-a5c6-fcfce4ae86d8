'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<path d="M14 25h14v2H14z" fill="currentColor"></path><path d="M7.17 26l-2.58 2.58L6 30l4-4l-4-4l-1.42 1.41L7.17 26z" fill="currentColor"></path><path d="M14 15h14v2H14z" fill="currentColor"></path><path d="M7.17 16l-2.58 2.58L6 20l4-4l-4-4l-1.42 1.41L7.17 16z" fill="currentColor"></path><path d="M14 5h14v2H14z" fill="currentColor"></path><path d="M7.17 6L4.59 8.58L6 10l4-4l-4-4l-1.42 1.41L7.17 6z" fill="currentColor"></path>', 6)
const _hoisted_8 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'CollapseCategories',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_8)
  }
})
