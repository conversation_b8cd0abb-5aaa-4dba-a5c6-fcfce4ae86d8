import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M433.9,175.6c-19-17.6-44.6-27.3-72.1-27.3l-5.6,0c-6.5-23.5-19.4-43.5-37.6-58.2C297.3,73,269.5,64,238.1,64\n\tc-32.7,0-63.2,11.7-86,32.9c-22.8,21.2-35.5,50-36.1,81.4C98.5,182.3,82.4,192,70,206.2c-14.2,16.2-22,36.6-22,57.4\n\tc0,44.6,34.9,82.6,77.4,86L101.2,382c0,0,0,0,0,0c-2.4,3.2-3.3,7.2-2.7,11.1c0.6,3.9,2.8,7.3,6,9.6c2.5,1.8,5.5,2.7,8.6,2.7\n\tc5.2,0,9.8-2.1,12.5-5.8l37.1-50h35.1l-55.3,75.1c-2.3,3.2-3.4,6.9-2.9,10.6c0.5,3.9,2.6,7.4,5.9,9.8c3.5,2.5,7.5,2.8,9,2.8\n\tc7.2,0,11.2-3.5,13.4-6.4l67.4-91.8h34.7L246,382c-2.4,3.3-3.4,7.2-2.7,11.1c0.6,3.9,2.8,7.3,6,9.6c2.5,1.8,5.5,2.7,8.6,2.7\n\tc5.2,0,9.8-2.1,12.5-5.8l37-50h35.1l-55.3,75.1c-2.3,3.2-3.4,7-2.9,10.6c0.5,3.8,2.6,7.2,5.9,9.6c2.6,1.9,5.9,3,8.9,3\n\tc5.1,0,9.7-2.2,12.5-6l69.7-95.1c22.4-4.5,43-16.6,58.1-34.5c15.9-18.8,24.7-42.6,24.7-67.1C464,218.4,453.3,193.6,433.9,175.6z'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'IosRainy',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
