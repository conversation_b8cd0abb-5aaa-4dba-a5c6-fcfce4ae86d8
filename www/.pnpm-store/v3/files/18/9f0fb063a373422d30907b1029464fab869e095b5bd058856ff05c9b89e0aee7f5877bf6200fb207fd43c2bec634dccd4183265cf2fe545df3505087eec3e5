import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.5 7a3.5 3.5 0 1 0 0 7h.5v3.5a.5.5 0 0 0 1 0V8h1v9.5a.5.5 0 0 0 1 0V8h.5a.5.5 0 0 0 0-1h-4zm-11-2a.5.5 0 0 0 0 1h15a.5.5 0 0 0 0-1h-15zm6.756 4H2.5a.5.5 0 0 0 0 1h6.527c.039-.347.117-.682.23-1zm.502 4H2.5a.5.5 0 0 0 0 1h8.171a4.528 4.528 0 0 1-.913-1z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'TextParagraph20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
