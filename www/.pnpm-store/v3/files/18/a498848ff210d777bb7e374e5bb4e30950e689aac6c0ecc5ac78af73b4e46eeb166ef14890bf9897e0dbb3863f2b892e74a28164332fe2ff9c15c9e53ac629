import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<path d="M12 16c3.87 0 7-3.13 7-7s-3.13-7-7-7s-7 3.13-7 7s3.13 7 7 7zm0-12c2.76 0 5 2.24 5 5s-2.24 5-5 5s-5-2.24-5-5s2.24-5 5-5z" fill="currentColor"></path><circle cx="10" cy="8" r="1" fill="currentColor"></circle><circle cx="14" cy="8" r="1" fill="currentColor"></circle><circle cx="12" cy="6" r="1" fill="currentColor"></circle><path d="M16 17H8c-.55 0-1 .45-1 1s.45 1 1 1h1c1.1 0 2 .9 2 2v1h2v-1c0-1.1.9-2 2-2h1c.55 0 1-.45 1-1s-.45-1-1-1z" fill="currentColor"></path>', 5)
const _hoisted_7 = [_hoisted_2]
export default defineComponent({
  name: 'SportsGolfRound',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_7)
  }
})
