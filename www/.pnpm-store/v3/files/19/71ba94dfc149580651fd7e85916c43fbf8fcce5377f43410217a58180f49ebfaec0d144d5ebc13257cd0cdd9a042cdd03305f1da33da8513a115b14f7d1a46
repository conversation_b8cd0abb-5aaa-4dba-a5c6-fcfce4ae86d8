import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.277 2.084a.5.5 0 0 0-.554 0a15.05 15.05 0 0 1-6.294 2.421A.5.5 0 0 0 3 5v4.5c0 3.891 2.307 6.73 6.82 8.467a.5.5 0 0 0 .36 0l.072-.028a5.528 5.528 0 0 1-1.386-1.475C5.6 14.867 4 12.557 4 9.5V5.428a15.969 15.969 0 0 0 5.6-2.082l.4-.249l.4.249A15.969 15.969 0 0 0 16 5.428V8.6c.358.183.693.404 1 .657V5a.5.5 0 0 0-.43-.495a15.05 15.05 0 0 1-6.293-2.421zm.42 13.512l4.899-4.9a3.5 3.5 0 0 0-4.9 4.9zm.707.707a3.5 3.5 0 0 0 4.9-4.9l-4.9 4.9zM13.5 18a4.5 4.5 0 1 1 0-9a4.5 4.5 0 0 1 0 9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ShieldProhibited20Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
