import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M13.75 2h-3.5a2.25 2.25 0 0 0-2.236 2H6.25A2.25 2.25 0 0 0 4 6.25v13.5A2.25 2.25 0 0 0 6.25 22h4.831a1.495 1.495 0 0 1 .046-1.115l1.791-3.982l.016-.035l.833-1.853V15h.007l1.85-4.115a1.5 1.5 0 0 1 2.736-.001L20 14.522V6.25A2.25 2.25 0 0 0 17.75 4h-1.764a2.25 2.25 0 0 0-2.236-2zm-3.5 1.5h3.5a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5zM12.508 22a.513.513 0 0 1-.218-.044a.5.5 0 0 1-.25-.661l1.794-3.99l.006-.013c0-.003.002-.005.003-.007l2.694-5.99a.5.5 0 0 1 .911 0l2.7 5.99a.46.46 0 0 1 .01.02l1.798 3.99a.5.5 0 1 1-.912.41L19.374 18h-4.756l-1.667 3.705a.5.5 0 0 1-.443.295zm4.485-9.282L15.067 17h3.856l-1.93-4.282z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ClipboardLetter24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
