'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M3.707 2.293a1 1 0 0 0-1.414 1.414L7.6 9.016A5 5 0 0 0 3 14v9a5 5 0 0 0 5 5h16c.757 0 1.474-.168 2.117-.469l2.176 2.176a1 1 0 0 0 1.414-1.414l-26-26zM29 23c0 .724-.154 1.412-.43 2.033L12.535 9H20V5h-8v3.464l-2-2V4a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v5h2a5 5 0 0 1 5 5v9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'BriefcaseOff32Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
