'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M16.951 2.504c.776 0 1.498.4 1.909 1.059L20.381 6h2.369A3.25 3.25 0 0 1 26 9.25v12.5A3.25 3.25 0 0 1 22.75 25H5.25A3.25 3.25 0 0 1 2 21.75V9.25A3.25 3.25 0 0 1 5.25 6h2.569L9.2 3.623a2.25 2.25 0 0 1 1.945-1.12h5.805zm0 1.5h-5.805a.75.75 0 0 0-.584.28l-.064.093l-1.6 2.75a.75.75 0 0 1-.648.374h-3A1.75 1.75 0 0 0 3.5 9.25v12.5c0 .966.784 1.75 1.75 1.75h17.5a1.75 1.75 0 0 0 1.75-1.75V9.25a1.75 1.75 0 0 0-1.75-1.75h-2.785a.75.75 0 0 1-.636-.353l-1.742-2.791a.75.75 0 0 0-.636-.353zM14 9.502a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11zm0 1.5a4 4 0 1 0 0 8a4 4 0 0 0 0-8z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Camera28Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
