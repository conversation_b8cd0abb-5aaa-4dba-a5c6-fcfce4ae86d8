import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M8.508 32a1.012 1.012 0 0 1-.448-.105a.999.999 0 0 1-.449-1.342l1.494-3a1.002 1.002 0 0 1 1.794.894l-1.493 3a1.002 1.002 0 0 1-.898.553z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ _createElementVNode(
  'path',
  {
    d: 'M29.844 13.035a1.518 1.518 0 0 0-1.23-.866a5.359 5.359 0 0 1-3.41-1.716a6.465 6.465 0 0 1-1.285-6.393a1.603 1.603 0 0 0-.3-1.546a1.454 1.454 0 0 0-1.36-.492l-.019.002a7.855 7.855 0 0 0-6.105 6.48A7.372 7.372 0 0 0 13.5 8a7.551 7.551 0 0 0-7.15 5.244A5.993 5.993 0 0 0 8 25h7.382l-1.276 2.553a1 1 0 1 0 1.788.894L17.619 25H19a5.955 5.955 0 0 0 5.88-7.146a7.5 7.5 0 0 0 4.867-3.3a1.538 1.538 0 0 0 .097-1.52zM19 23H8a3.993 3.993 0 0 1-.673-7.93l.663-.112l.145-.656a5.496 5.496 0 0 1 10.73 0l.145.656l.663.113A3.993 3.993 0 0 1 19 23zm5.15-7.048a5.964 5.964 0 0 0-3.5-2.708a7.508 7.508 0 0 0-2.621-3.694a6.01 6.01 0 0 1 3.77-5.334a8.458 8.458 0 0 0 1.94 7.597a7.4 7.4 0 0 0 3.901 2.228a5.44 5.44 0 0 1-3.49 1.911z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = [_hoisted_2, _hoisted_3]
export default defineComponent({
  name: 'RainScatteredNight',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_4)
  }
})
