import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  null,
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M256,320c37.712,0,68.571-30.924,68.571-68.714V100.714C324.571,62.924,293.712,32,256,32s-68.571,30.924-68.571,68.714\r\n\t\tv150.572C187.429,289.076,218.288,320,256,320z M377.139,244.548c0,68.714-58.282,116.815-121.139,116.815\r\n\t\ts-121.139-48.102-121.139-116.815H96c0,77.873,61.719,143.153,137.144,153.465V480h45.713v-81.987\r\n\t\tC354.281,386.561,416,322.421,416,244.548H377.139z'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'MdMic',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
