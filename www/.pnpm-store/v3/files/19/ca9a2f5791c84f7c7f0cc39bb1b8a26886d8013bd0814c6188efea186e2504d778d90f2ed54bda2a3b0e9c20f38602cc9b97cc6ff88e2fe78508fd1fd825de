import { c, cB, cE, cM } from "../../../_utils/cssr/index.mjs";
// vars:
// --n-resize-trigger-color
// --n-resize-trigger-color-hover
// --n-bezier
export default cB('split', `
 display: flex;
 width: 100%;
 height: 100%;
`, [cM('horizontal', `
 flex-direction: row;
 `), cM('vertical', `
 flex-direction: column;
 `), cB('split-pane-1', `
 overflow: hidden;
 `), cB('split-pane-2', `
 overflow: hidden;
 flex: 1;
 `), cE('resize-trigger', `
 background-color: var(--n-resize-trigger-color);
 transition: background-color .3s var(--n-bezier);
 `, [cM('hover', `
 background-color: var(--n-resize-trigger-color-hover);
 `), c('&:hover', `
 background-color: var(--n-resize-trigger-color-hover);
 `)])]);