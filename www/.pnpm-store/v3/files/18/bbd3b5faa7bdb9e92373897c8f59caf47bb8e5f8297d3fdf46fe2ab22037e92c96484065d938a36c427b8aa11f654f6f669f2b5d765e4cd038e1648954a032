'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  null,
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('g', null, [
      /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
        d: 'M387.581,139.712L356.755,109L216.913,248.319l30.831,30.719L387.581,139.712z M481.172,109L247.744,340.469\r\n\t\t\tl-91.39-91.051l-30.827,30.715L247.744,403L512,139.712L481.172,109z M0,280.133L123.321,403l30.829-30.713L31.934,249.418\r\n\t\t\tL0,280.133z'
      })
    ])
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'MdDoneAll',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
