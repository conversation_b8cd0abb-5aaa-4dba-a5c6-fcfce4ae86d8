import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8 2a3 3 0 0 0-3 3a.5.5 0 0 0 1 0a2 2 0 0 1 4 0c0 .676-.14 1.08-.315 1.36c-.183.293-.428.494-.747.75l-.02.015c-.302.242-.672.537-.951.985C7.673 8.58 7.5 9.176 7.5 10v.5a.5.5 0 0 0 1 0V10c0-.676.14-1.08.315-1.36c.183-.293.428-.494.747-.75l.02-.015c.302-.242.672-.537.951-.985C10.827 6.42 11 5.824 11 5a3 3 0 0 0-3-3zm0 12a.75.75 0 1 0 0-1.5a.75.75 0 0 0 0 1.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Question16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
