'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<circle cx="14" cy="6" r="1" fill="currentColor"></circle><path d="M13.8 11.48l.2.02c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5l.02.2c.09.67.61 1.19 1.28 1.28zM14 3.5c.28 0 .5-.22.5-.5s-.22-.5-.5-.5s-.5.22-.5.5s.22.5.5.5zm-4 0c.28 0 .5-.22.5-.5s-.22-.5-.5-.5s-.5.22-.5.5s.22.5.5.5z" fill="currentColor"></path><circle cx="18" cy="10" r="1" fill="currentColor"></circle><circle cx="18" cy="6" r="1" fill="currentColor"></circle><path d="M21 10.5c.28 0 .5-.22.5-.5s-.22-.5-.5-.5s-.5.22-.5.5s.22.5.5.5z" fill="currentColor"></path><circle cx="10" cy="6" r="1" fill="currentColor"></circle><circle cx="18" cy="14" r="1" fill="currentColor"></circle><circle cx="6" cy="18" r="1" fill="currentColor"></circle><path d="M14 20.5c-.28 0-.5.22-.5.5s.22.5.5.5s.5-.22.5-.5s-.22-.5-.5-.5zm7-7c-.28 0-.5.22-.5.5s.22.5.5.5s.5-.22.5-.5s-.22-.5-.5-.5zm-18 0c-.28 0-.5.22-.5.5s.22.5.5.5s.5-.22.5-.5s-.22-.5-.5-.5z" fill="currentColor"></path><circle cx="10" cy="18" r="1" fill="currentColor"></circle><path d="M3 9.5c-.28 0-.5.22-.5.5s.22.5.5.5s.5-.22.5-.5s-.22-.5-.5-.5zm7 11c-.28 0-.5.22-.5.5s.22.5.5.5s.5-.22.5-.5s-.22-.5-.5-.5z" fill="currentColor"></path><circle cx="6" cy="14" r="1" fill="currentColor"></circle><path d="M2.5 5.27L6 8.77l.28.28L6 9c-.55 0-1 .45-1 1s.45 1 1 1s1-.45 1-1c0-.1-.03-.19-.06-.28l2.81 2.81c-.71.11-1.25.73-1.25 1.47c0 .83.67 1.5 1.5 1.5c.74 0 1.36-.54 1.47-1.25l2.81 2.81A.875.875 0 0 0 14 17c-.55 0-1 .45-1 1s.45 1 1 1s1-.45 1-1c0-.1-.03-.19-.06-.28l3.78 3.78h.01l1.41-1.41L3.91 3.86L2.5 5.27z" fill="currentColor"></path>', 13)
const _hoisted_15 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'BlurOffOutlined',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_15)
  }
})
