{"name": "@vue/language-core", "version": "2.0.6", "license": "MIT", "files": ["**/*.js", "**/*.d.ts"], "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/language-core"}, "dependencies": {"@volar/language-core": "~2.1.2", "@vue/compiler-dom": "^3.4.0", "@vue/shared": "^3.4.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "devDependencies": {"@types/minimatch": "^5.1.2", "@types/node": "latest", "@types/path-browserify": "^1.0.1", "@vue/compiler-sfc": "^3.4.0"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "gitHead": "feb990ccec85f6330bba37c8b1d1287f0980274c"}