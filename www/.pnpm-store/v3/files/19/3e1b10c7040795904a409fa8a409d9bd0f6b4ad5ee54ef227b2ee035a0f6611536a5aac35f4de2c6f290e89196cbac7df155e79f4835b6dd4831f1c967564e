'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M13.577 2.378a3 3 0 0 0-2.154 0L7.962 3.709A1.5 1.5 0 0 0 7 5.109v1.1a4.001 4.001 0 0 1 1 0v-1.1a.5.5 0 0 1 .32-.467l3.462-1.331a2 2 0 0 1 1.436 0l3.461 1.331a.5.5 0 0 1 .32.467v4.49c.358.182.693.403 1 .657V5.109a1.5 1.5 0 0 0-.961-1.4l-3.461-1.331zm-1.539 6.331a1.5 1.5 0 0 1 .713.574a5.468 5.468 0 0 0-.953.425a.502.502 0 0 0-.119-.066L8.218 8.311a2 2 0 0 0-1.436 0L3.32 9.642A.5.5 0 0 0 3 10.11v4.78a.5.5 0 0 0 .32.467l3.462 1.33a2 2 0 0 0 1.436 0l1.075-.413c.108.32.245.625.408.915l-1.124.432a3 3 0 0 1-2.154 0L2.962 16.29a1.5 1.5 0 0 1-.962-1.4v-4.78a1.5 1.5 0 0 1 .962-1.4l3.46-1.331a3 3 0 0 1 2.155 0l3.461 1.331zm-1.593 1.297a.5.5 0 0 1 .607.208a5.535 5.535 0 0 0-.964 1.001L8 12.018v2.49a.5.5 0 0 1-1 0v-2.49L4.195 10.94a.5.5 0 1 1 .36-.933L7.5 11.14l2.945-1.133zm5.646-4.713a.5.5 0 0 0-.646-.287L12.679 6.07a.5.5 0 0 1-.359 0L9.554 5.006a.5.5 0 0 0-.359.933l2.766 1.064a1.5 1.5 0 0 0 1.077 0l2.766-1.064a.5.5 0 0 0 .287-.646zM19 14.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0zm-2.147.354l.003-.002a.5.5 0 0 0 .144-.349v-.006a.5.5 0 0 0-.146-.35l-2-2a.5.5 0 0 0-.708.707l1.147 1.146H12.5a.5.5 0 0 0 0 1h2.793l-1.147 1.146a.5.5 0 0 0 .708.708l2-2z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'BoxMultipleArrowRight20Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
