'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M6 12.25A6.25 6.25 0 0 1 12.25 6h5.5a1.25 1.25 0 1 1 0 2.5h-5.5a3.75 3.75 0 0 0-3.75 3.75v5.5a1.25 1.25 0 1 1-2.5 0v-5.5zm23-5c0-.69.56-1.25 1.25-1.25h5.5A6.25 6.25 0 0 1 42 12.25v5.5a1.25 1.25 0 1 1-2.5 0v-5.5a3.75 3.75 0 0 0-3.75-3.75h-5.5c-.69 0-1.25-.56-1.25-1.25zM7.25 29c.69 0 1.25.56 1.25 1.25v5.5a3.75 3.75 0 0 0 3.75 3.75h5.5a1.25 1.25 0 1 1 0 2.5h-5.5A6.25 6.25 0 0 1 6 35.75v-5.5c0-.69.56-1.25 1.25-1.25zm33.5 0c.69 0 1.25.56 1.25 1.25v5.5A6.25 6.25 0 0 1 35.75 42h-5.5a1.25 1.25 0 1 1 0-2.5h5.5a3.75 3.75 0 0 0 3.75-3.75v-5.5c0-.69.56-1.25 1.25-1.25zM27 24.5a3 3 0 1 0-6 0a3 3 0 0 0 6 0zm.865-8.887a2.25 2.25 0 0 0-1.94-1.11h-3.803a2.25 2.25 0 0 0-1.917 1.073L19.33 17h-2.08A3.25 3.25 0 0 0 14 20.25v9.5A3.25 3.25 0 0 0 17.25 33h13.5A3.25 3.25 0 0 0 34 29.75v-9.5A3.25 3.25 0 0 0 30.75 17h-2.07l-.815-1.387zM19.5 24.5a4.5 4.5 0 1 1 9 0a4.5 4.5 0 0 1-9 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ScanCamera48Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
