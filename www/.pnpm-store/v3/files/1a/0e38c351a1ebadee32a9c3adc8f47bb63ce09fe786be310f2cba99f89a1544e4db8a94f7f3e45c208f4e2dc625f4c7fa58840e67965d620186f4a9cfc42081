import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.457 31.51a2.672 2.672 0 0 0 3.08.008c.405-.288.723-.682.917-1.139l1.236-3.769a6.159 6.159 0 0 1 3.9-3.9l3.631-1.183a2.67 2.67 0 0 0 0-5.036l-3.7-1.193a6.176 6.176 0 0 1-3.895-3.888l-1.179-3.629a2.663 2.663 0 0 0-.976-1.291a2.713 2.713 0 0 0-3.085 0a2.68 2.68 0 0 0-.987 1.32l-1.193 3.667a6.168 6.168 0 0 1-3.796 3.818l-3.627 1.178a2.67 2.67 0 0 0 .03 5.047l3.587 1.165a6.185 6.185 0 0 1 3.902 3.91l1.18 3.623c.183.521.524.973.976 1.292zm15.419 9.132c.329.233.721.358 1.124.359l.005.003a1.946 1.946 0 0 0 1.844-1.328l.569-1.75a2.382 2.382 0 0 1 1.499-1.502l1.79-.582a1.947 1.947 0 0 0 .94-2.958a1.961 1.961 0 0 0-1.005-.73l-1.757-.569a2.374 2.374 0 0 1-1.5-1.5l-.582-1.789a1.944 1.944 0 0 0-3.679.03l-.572 1.757a2.377 2.377 0 0 1-1.46 1.495l-1.79.582a1.944 1.944 0 0 0 .029 3.677l1.752.57a2.369 2.369 0 0 1 1.5 1.506l.582 1.788c.134.38.382.709.71.941z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Sparkle48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
