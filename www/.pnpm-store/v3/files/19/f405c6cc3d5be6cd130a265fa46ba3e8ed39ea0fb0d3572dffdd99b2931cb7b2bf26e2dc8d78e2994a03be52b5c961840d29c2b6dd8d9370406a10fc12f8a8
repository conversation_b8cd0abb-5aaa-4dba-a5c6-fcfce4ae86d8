'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M6.98 6.377l4.83-4.83a1.87 1.87 0 1 1 2.644 2.646l-4.83 4.829a2.197 2.197 0 0 1-1.02.578l-1.498.374a.89.89 0 0 1-1.079-1.078l.375-1.498c.096-.386.296-.74.578-1.02zM4 10.997h2.74c.093.007.188.008.284 0H12a1 1 0 0 0 1-1V7.061l1-1v3.936a2 2 0 0 1-2 2h-1.997V13h1.5a.5.5 0 1 1 0 1H4.505a.5.5 0 0 1 0-1h1.499v-1.003H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h5.943l-1 1H4a1 1 0 0 0-1 1v5.997a1 1 0 0 0 1 1zm5.003 1H7.005V13h1.998v-1.003z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'DesktopEdit16Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
