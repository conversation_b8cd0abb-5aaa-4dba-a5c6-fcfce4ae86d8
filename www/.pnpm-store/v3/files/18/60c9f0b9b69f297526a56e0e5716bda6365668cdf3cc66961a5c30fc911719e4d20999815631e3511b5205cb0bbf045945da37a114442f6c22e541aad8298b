import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M35.795 9.84c-4.174-4.602-11.303-4.505-15.855.67l-.017.02l-8.558 8.958h12.38a1.25 1.25 0 0 1 0 2.5H8.198c-.69 0-1.25-.56-1.25-1.25V5.268a1.25 1.25 0 0 1 2.5 0v12.606l8.631-9.035c5.45-6.174 14.307-6.478 19.567-.679c5.24 5.778 3.622 13.316-.014 17.195l-.01.01l-8.978 9.173l-.005.005l-8.75 8.837l-.005.004l-.25.25a1.25 1.25 0 1 1-1.768-1.767l.246-.246l.005-.005l8.74-8.827l.005-.005l8.955-9.148c2.919-3.124 4.172-9.17-.023-13.796z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ArrowUndo48Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
