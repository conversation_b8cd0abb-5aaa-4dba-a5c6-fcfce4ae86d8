import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M4.5 2A2.5 2.5 0 0 0 2 4.5v5c0 .51.152.983.414 1.378l3.377-3.376a1.71 1.71 0 0 1 2.418 0l3.377 3.377c.262-.396.414-.87.414-1.379v-5A2.5 2.5 0 0 0 9.5 2h-5zM10 5a1 1 0 1 1-2 0a1 1 0 0 1 2 0zm.879 6.586L7.502 8.209a.71.71 0 0 0-1.004 0L3.12 11.586c.395.261.87.414 1.379.414h5c.51 0 .983-.152 1.379-.414zM4.999 13c.457.607 1.183 1 2 1h2.5A4.5 4.5 0 0 0 14 9.5V6c0-.818-.392-1.544-1-2v5.5A3.5 3.5 0 0 1 9.5 13H5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ImageMultiple16Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
