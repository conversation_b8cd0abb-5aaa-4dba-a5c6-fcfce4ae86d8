'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M3 6.75A3.75 3.75 0 0 1 6.75 3h14.5A3.75 3.75 0 0 1 25 6.75v.75h-8.647l-1.19-1.058a1.75 1.75 0 0 0-2.326 0L11.647 7.5H3v-.75zM3 9h7.168a1.75 1.75 0 0 0 2.082 2.428v5.145A1.75 1.75 0 0 0 10.168 19H3V9zm14.558 8.087A1.75 1.75 0 0 1 17.832 19H25V9h-7.168a1.75 1.75 0 0 1-2.082 2.428v5.145a1.75 1.75 0 0 1 1.808.514zm-4.72 4.471l-1.19-1.058H3v.75A3.75 3.75 0 0 0 6.75 25h14.5A3.75 3.75 0 0 0 25 21.25v-.75h-8.647l-1.185 1.053l-.002.002a1.747 1.747 0 0 1-2.3.027l-.009-.006l-.02-.018zM14.75 9.42v9.16l1.002-.89a.75.75 0 0 1 .996 1.12l-2.247 1.998a.748.748 0 0 1-.987.013l-.012-.01l-2.25-2a.75.75 0 1 1 .996-1.122l1.002.89V9.42l-1.002.89a.75.75 0 0 1-.996-1.12l2.25-2a.75.75 0 0 1 .996 0l2.25 2a.75.75 0 0 1-.996 1.12l-1.002-.89zM14.067 22h-.14z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TableResizeRow28Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
