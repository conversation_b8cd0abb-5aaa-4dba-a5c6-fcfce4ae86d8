'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M15.347 29.985a13.928 13.928 0 0 1-4.205-.852l.694-1.875a11.929 11.929 0 0 0 3.603.73zm4.265-.456l-.514-1.932a11.915 11.915 0 0 0 3.363-1.483l1.078 1.685a13.915 13.915 0 0 1-3.927 1.73zM7.395 27.044a14.063 14.063 0 0 1-2.94-3.122l1.648-1.133a12.052 12.052 0 0 0 2.522 2.678zm-4.798-6.99A14.051 14.051 0 0 1 2 16H4a12.041 12.041 0 0 0 .512 3.476z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M26 17l-1.414 1.414L26.172 20H15.816a2.987 2.987 0 0 0-.275-.576l4.481-5.601A2.968 2.968 0 0 0 21 14a3 3 0 1 0-2.816-4h-4.368a2.982 2.982 0 0 0-5.632 0H2v2h6.184a2.982 2.982 0 0 0 5.632 0h4.368a2.987 2.987 0 0 0 .274.576l-4.48 5.601A2.968 2.968 0 0 0 13 18a3 3 0 1 0 2.816 4h10.356l-1.586 1.586L26 25l4-4zm-5-7a1 1 0 1 1-1 1a1 1 0 0 1 1-1zm-10 2a1 1 0 1 1 1-1a1 1 0 0 1-1 1zm2 10a1 1 0 1 1 1-1a1 1 0 0 1-1 1z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M28 16a12.041 12.041 0 0 0-.512-3.476l1.915-.578A14.055 14.055 0 0 1 30 16zm-2.104-6.789a12.052 12.052 0 0 0-2.522-2.678l1.23-1.577a14.063 14.063 0 0 1 2.94 3.122zM9.54 5.886L8.461 4.201a13.915 13.915 0 0 1 3.927-1.73l.514 1.932A11.915 11.915 0 0 0 9.54 5.886zm10.625-1.144a11.929 11.929 0 0 0-3.603-.73l.092-1.997a13.928 13.928 0 0 1 4.205.851z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_5 = [_hoisted_2, _hoisted_3, _hoisted_4]
exports.default = (0, vue_1.defineComponent)({
  name: 'SubflowLocal',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_5)
  }
})
