'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M160,64v257.6c-8.2-2.7-17.2-4.1-26.6-4.1c-38.3,0-69.4,27.1-69.4,65.4c0,38.3,31.1,65.1,69.4,65.1\r\n\tc38.3,0,69.6-28.2,69.6-69.1V200h202v121.6c-8.2-2.7-17.2-4.1-26.6-4.1c-38.3,0-69.4,27.1-69.4,65.4c0,38.3,31.1,65.1,69.4,65.1\r\n\tc38.3,0,69.6-28.2,69.6-69.1V64H160z M405,160H203v-53h202V160z'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'MdMusicalNotes',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
