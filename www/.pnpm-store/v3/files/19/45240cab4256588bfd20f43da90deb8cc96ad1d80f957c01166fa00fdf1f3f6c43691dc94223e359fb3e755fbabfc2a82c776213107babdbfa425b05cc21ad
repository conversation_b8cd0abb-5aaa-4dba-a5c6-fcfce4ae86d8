'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M8.41 14.96c-.19 0-.37-.03-.52-.08a1.07 1.07 0 0 1-.4-.24c-.11-.1-.2-.22-.26-.37c-.06-.14-.09-.3-.09-.47h-1.3c0 .36.07.68.21.95c.14.27.33.5.56.69c.24.18.51.32.82.41c.3.1.62.15.96.15c.37 0 .72-.05 1.03-.15c.32-.1.6-.25.83-.44s.42-.43.55-.72s.2-.61.2-.97c0-.19-.02-.38-.07-.56a1.67 1.67 0 0 0-.23-.51c-.1-.16-.24-.3-.4-.43c-.17-.13-.37-.23-.61-.31a2.098 2.098 0 0 0 .89-.75c.1-.15.17-.3.22-.46s.07-.32.07-.48c0-.36-.06-.68-.18-.96s-.29-.51-.51-.69c-.2-.19-.47-.33-.77-.43C9.1 8.05 8.76 8 8.39 8c-.36 0-.69.05-1 .16c-.3.11-.57.26-.79.45c-.21.19-.38.41-.51.67c-.12.26-.18.54-.18.85h1.3c0-.17.03-.32.09-.45s.14-.25.25-.34s.23-.17.38-.22s.3-.08.48-.08c.4 0 .7.1.89.31c.19.2.29.49.29.86c0 .18-.03.34-.08.49s-.14.27-.25.37c-.11.1-.25.18-.41.24c-.16.06-.36.09-.58.09H7.5v1.03h.77c.22 0 .42.02.6.07s.33.13.45.23c.12.11.22.24.29.4s.1.35.1.57c0 .41-.12.72-.35.93c-.23.23-.55.33-.95.33zm9.3-4.72c-.18-.47-.43-.87-.75-1.2c-.32-.33-.7-.59-1.14-.77c-.43-.18-.92-.27-1.46-.27H12v8h2.3c.55 0 1.06-.09 1.51-.27s.84-.43 1.16-.76c.32-.33.57-.73.74-1.19c.17-.47.26-.99.26-1.57v-.4c0-.58-.09-1.1-.26-1.57zm-1.13 1.96c0 .42-.05.79-.14 1.13c-.1.33-.24.62-.43.85s-.43.41-.71.53c-.29.12-.62.18-.99.18h-.91V9.11h.97c.72 0 1.27.23 1.64.69c.38.46.57 1.12.57 1.99v.41zm-1.43-8.36l1.33-1.33c3.09 1.46 5.34 4.37 5.89 7.86c.06.41.44.69.86.62c.41-.06.69-.45.62-.86c-.6-3.81-2.96-7.01-6.24-8.75A11.81 11.81 0 0 0 11.34.02l3.81 3.82zm-6.3 16.31l-1.33 1.33a10.504 10.504 0 0 1-5.89-7.86a.737.737 0 0 0-.86-.62c-.41.06-.69.45-.62.86c.6 3.81 2.96 7.01 6.24 8.75c1.67.89 3.83 1.51 6.27 1.36l-3.81-3.82z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Md3DRotationRound',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
