import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.987 2.89a.75.75 0 1 0-1.474-.28L8.494 7.999L3.75 8a.75.75 0 1 0 0 1.5l4.46-.002l-.946 5l-4.514.002a.75.75 0 0 0 0 1.5l4.23-.002l-.967 5.116a.75.75 0 1 0 1.474.278l1.02-5.395h2.668c.125-.529.314-1.032.56-1.501l-2.944.001l.946-5l5.474-.002l-.394 2.083a6.454 6.454 0 0 1 1.62-.491l.3-1.593l4.513-.002a.75.75 0 1 0 0-1.5l-4.229.002l.966-5.104a.75.75 0 0 0-1.474-.28l-1.018 5.385l-5.474.002l.966-5.107zM23 17.5a5.5 5.5 0 1 1-11 0a5.5 5.5 0 0 1 11 0zm-7.146-2.353a.5.5 0 0 0-.708.707l1.647 1.646l-1.647 1.647a.5.5 0 0 0 .708.707l1.646-1.647l1.646 1.647a.5.5 0 0 0 .708-.707L18.207 17.5l1.647-1.646a.5.5 0 0 0-.708-.707L17.5 16.793l-1.646-1.646z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'NumberSymbolDismiss24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
