'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4 11.75A4.75 4.75 0 0 1 8.75 7h30.5A4.75 4.75 0 0 1 44 11.75v7.895a3.5 3.5 0 0 0-1-.145h-7a3.5 3.5 0 0 0-3.5 3.5v2.837a2 2 0 0 0-2 2V35H8.75A4.75 4.75 0 0 1 4 30.25v-18.5zM11.25 41h21.793l-1.579-2.5H11.25a1.25 1.25 0 1 0 0 2.5zM35 23v5h-1a1 1 0 0 0-1 1v6.96a1 1 0 0 0 .154.535L36 41v4a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-4l2.846-4.505A1 1 0 0 0 46 35.96V29a1 1 0 0 0-1-1h-1v-5a1 1 0 0 0-1-1h-7a1 1 0 0 0-1 1zm2.5 1.5h4V28h-4v-3.5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TvUsb48Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
