import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M22 16a7 7 0 1 0-6.406-4.174l-2.83 2.2a5.5 5.5 0 1 0-.269 7.252l4.776 2.68a4.5 4.5 0 1 0 1.09-1.682l-4.775-2.679A5.483 5.483 0 0 0 14 17.5c0-.59-.093-1.16-.266-1.694l2.929-2.277A6.985 6.985 0 0 0 22 16z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Molecule32Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
