'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M9.309 4H19.5A5.5 5.5 0 0 1 25 9.5v16.66a3.6 3.6 0 0 0 2-3.227V9.5A7.5 7.5 0 0 0 19.5 2h-6.967A3.6 3.6 0 0 0 9.31 4zM8.75 6A3.75 3.75 0 0 0 5 9.75V29a1 1 0 0 0 1.591.806l6.945-5.092l7.92 5.126A1 1 0 0 0 23 29V9.75A3.75 3.75 0 0 0 19.25 6H8.75z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'BookmarkMultiple32Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
