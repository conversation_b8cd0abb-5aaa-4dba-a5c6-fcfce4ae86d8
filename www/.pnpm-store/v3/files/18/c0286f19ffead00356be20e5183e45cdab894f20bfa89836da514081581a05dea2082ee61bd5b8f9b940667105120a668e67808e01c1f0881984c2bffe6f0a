import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<path d="M24 28v-2h2v2z" fill="currentColor"></path><path d="M18 24v-2h2v2z" fill="currentColor"></path><path d="M18 30h4v-2h-2v-2h-2v4z" fill="currentColor"></path><path d="M26 26v-4h2v4z" fill="currentColor"></path><path d="M28 26h2v4h-4v-2h2v-2z" fill="currentColor"></path><path d="M26 20v-2h4v4h-2v-2h-2z" fill="currentColor"></path><path d="M24 20h-2v4h-2v2h4v-6z" fill="currentColor"></path><path d="M18 20v-2h4v2z" fill="currentColor"></path><path d="M6 22h4v4H6z" fill="currentColor"></path><path d="M14 30H2V18h12zM4 28h8v-8H4z" fill="currentColor"></path><path d="M22 6h4v4h-4z" fill="currentColor"></path><path d="M30 14H18V2h12zm-10-2h8V4h-8z" fill="currentColor"></path><path d="M6 6h4v4H6z" fill="currentColor"></path><path d="M14 14H2V2h12zM4 12h8V4H4z" fill="currentColor"></path>', 14)
const _hoisted_16 = [_hoisted_2]
export default defineComponent({
  name: 'QrCode',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_16)
  }
})
