'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M6.253 6.682A.75.75 0 0 1 7 6h3.25a.75.75 0 0 1 0 1.5H7.685l-.103 1.132l.44-.058a2.85 2.85 0 0 1 3.228 2.825c0 1.581-1.298 2.851-2.865 2.851c-.76 0-1.497-.3-2.037-.841l-.378-.379a.75.75 0 1 1 1.06-1.06l.379.378c.256.256.61.402.976.402c.752 0 1.365-.611 1.365-1.35a1.35 1.35 0 0 0-1.53-1.34l-1.37.183a.75.75 0 0 1-.847-.81l.25-2.75zM5 13.5a.5.5 0 1 1-1 0a.5.5 0 0 1 1 0zm8.28-3.28a.75.75 0 1 0-1.06 1.06l.72.72l-.72.72a.75.75 0 1 0 1.06 1.06l.72-.72l.72.72a.75.75 0 1 0 1.06-1.06l-.72-.72l.72-.72a.75.75 0 1 0-1.06-1.06l-.72.72l-.72-.72z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Multiplier5X20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
