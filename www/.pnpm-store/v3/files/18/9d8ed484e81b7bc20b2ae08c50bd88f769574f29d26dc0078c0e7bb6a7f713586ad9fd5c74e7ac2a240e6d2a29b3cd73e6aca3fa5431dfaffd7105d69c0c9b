import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2.993 6.886A28.511 28.511 0 0 1 10 6c2.335 0 4.67.296 7.007.886c.588.149.993.682.993 1.284v2.55c0 1.804-1.442 3.28-3.236 3.28H12.76a2.11 2.11 0 0 1-1.596-.732l-.53-.612a.834.834 0 0 0-1.27 0l-.529.611A2.11 2.11 0 0 1 7.24 14H5.237C3.442 14 2 12.524 2 10.72V8.17c0-.602.404-1.135.993-1.284zM10.75 8.75a.75.75 0 1 0-1.5 0a.75.75 0 0 0 1.5 0zM4.5 9a.5.5 0 0 0 0 1H6a.5.5 0 0 0 0-1H4.5zm9 .499a.5.5 0 0 0 .499.501l1.5.003a.5.5 0 0 0 .002-1L14.001 9a.5.5 0 0 0-.501.499z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'HeadsetVr20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
