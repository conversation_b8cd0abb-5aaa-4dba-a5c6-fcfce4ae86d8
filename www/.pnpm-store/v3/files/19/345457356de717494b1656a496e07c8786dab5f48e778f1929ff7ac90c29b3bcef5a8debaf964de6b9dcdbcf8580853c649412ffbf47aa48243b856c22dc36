'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M7 8.5c0-.232.052-.45.146-.647l-5-5a.5.5 0 1 1 .708-.707l15 15a.5.5 0 0 1-.708.708l-3.706-3.706l-.287.276a.5.5 0 0 0-.153.36V16.5a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 7 16.5v-1.716a.5.5 0 0 0-.153-.36L3.144 10.86A.5.5 0 0 1 3.49 10h5.802l-1-1H7v-.5zm3.293 2.5H4.73l2.81 2.703A1.5 1.5 0 0 1 8 14.784V16.5a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-1.716a1.5 1.5 0 0 1 .46-1.08l.273-.264l-2.44-2.44zM12 4a2 2 0 1 1-4 0a2 2 0 0 1 4 0zm-1 0a1 1 0 1 0-2 0a1 1 0 0 0 2 0zm-.879 4H11.5a.5.5 0 0 1 .5.5V9h1v-.5A1.5 1.5 0 0 0 11.5 7H9.121l1 1zm4.76 4.76l-.707-.707L15.27 11H13.12l-1-1h4.388a.5.5 0 0 1 .347.86l-1.974 1.9z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'PresenterOff20Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
