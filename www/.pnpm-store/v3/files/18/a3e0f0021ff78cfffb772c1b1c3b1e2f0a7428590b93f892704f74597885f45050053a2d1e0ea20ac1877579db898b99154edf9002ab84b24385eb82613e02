'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M4.374 5.989a7.631 7.631 0 0 1 10.66.13l.966.967l.86-.86A7.694 7.694 0 0 1 29.456 14.5h-2.214a5.694 5.694 0 0 0-8.968-6.86l-1.567 1.567a1 1 0 0 1-1.414 0l-1.674-1.674A5.631 5.631 0 0 0 4.86 14.5H2.61a7.634 7.634 0 0 1 1.763-8.511zM22.708 19.5h2.76l-8.744 9.19a1 1 0 0 1-1.454-.006L6.662 19.5h2.481c.084 0 .167-.004.249-.012l6.614 7.056l6.702-7.044zm-9.814-7.947a1 1 0 0 0-1.752-.068L8.434 16H2.667a1 1 0 1 0 0 2H9a1 1 0 0 0 .857-.485l2.063-3.439l3.186 6.371a1 1 0 0 0 1.703.141l3.371-4.636l2.18 1.816A1 1 0 0 0 23 18h6a1 1 0 0 0 0-2h-5.638l-2.722-2.268a1 1 0 0 0-1.449.18l-3.032 4.17l-3.265-6.53z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'HeartPulse32Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
