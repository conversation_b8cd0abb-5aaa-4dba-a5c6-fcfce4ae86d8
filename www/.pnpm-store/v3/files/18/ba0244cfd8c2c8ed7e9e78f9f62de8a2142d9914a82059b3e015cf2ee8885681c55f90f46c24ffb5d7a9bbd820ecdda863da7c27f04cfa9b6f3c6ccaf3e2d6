'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M16.79 5.8L14 3h7v7l-2.79-2.8l-4.09 4.09l-1.41-1.41l4.08-4.08zM19 12v4.17l2 2V12h-2zm.78 10.61L18.17 21H3V5.83L1.39 4.22L2.8 2.81l18.38 18.38l-1.4 1.42zM16.17 19l-4.88-4.88l-1.59 1.59l-1.41-1.41l1.59-1.59L5 7.83V19h11.17zM7.83 5H12V3H5.83l2 2z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'OpenInNewOffSharp',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
