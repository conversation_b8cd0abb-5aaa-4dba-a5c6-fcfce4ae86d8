'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createStaticVNode)('<path d="M20 17v-2a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2zm-10-2h8v2h-8z" fill="currentColor"></path><path d="M23.42 24.48A2 2 0 0 0 21 23.06l-7.73 2.07a2 2 0 0 0-1.42 2.44l.52 1.93a2 2 0 0 0 1.9 1.5a3 3 0 0 0 .52-.07l7.73-2.07a2 2 0 0 0 1.41-2.45zM14.27 29l-.51-1.94L21.48 25l.52 1.92z" fill="currentColor"></path><path d="M13.24 6.86L21 8.93a2.24 2.24 0 0 0 .51.07a2 2 0 0 0 1.94-1.48L24 5.58a2 2 0 0 0-1.41-2.45l-7.8-2.07a2 2 0 0 0-2.45 1.41l-.52 1.93a2 2 0 0 0 1.42 2.44zm1-3.86L22 5.07L21.48 7l-7.72-2.07z" fill="currentColor"></path><path d="M14 21h2v2h-2z" fill="currentColor"></path><path d="M14 9h2v2h-2z" fill="currentColor"></path>', 5)
const _hoisted_7 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'CobbAngle',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_7)
  }
})
