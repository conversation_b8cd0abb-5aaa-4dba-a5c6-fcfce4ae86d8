import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M17.75 2A2.25 2.25 0 0 1 20 4.25v15.505a2.25 2.25 0 0 1-2.25 2.25H6.25A2.25 2.25 0 0 1 4 19.755V4.25a2.25 2.25 0 0 1 2.096-2.245L6.25 2h11.5zm.75 14h-13v3.755c0 .414.336.75.75.75h11.5a.75.75 0 0 0 .75-.75V16zM7.751 17.5h8.499a.75.75 0 0 1 .102 1.493L16.25 19H7.751a.75.75 0 0 1-.101-1.493l.101-.007h8.499h-8.499zm9.999-14H6.25l-.102.007a.75.75 0 0 0-.648.743V14.5H8v-2.255c0-.647.492-1.179 1.122-1.243l.128-.007h5.5c.647 0 1.18.492 1.243 1.123l.007.127V14.5h2.5V4.25a.75.75 0 0 0-.75-.75zM12 4.996a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Patient24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
