'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M13.03 19.98a1.25 1.25 0 0 0 1.449-.998v-.003l.014-.056a3.308 3.308 0 0 1 .59-1.14c.473-.589 1.369-1.283 3.166-1.283c1.596 0 2.6.522 3.167 1.242c.58.736.884 1.906.602 3.546c-.165.964-.648 1.539-1.408 2.026c-.62.398-1.316.681-2.147 1.019a53.44 53.44 0 0 0-1.065.442c-1.231.53-2.62 1.238-3.678 2.503c-1.087 1.3-1.72 3.053-1.72 5.472A1.25 1.25 0 0 0 13.25 34h10a1.25 1.25 0 1 0 0-2.5h-8.672c.164-1.224.57-2.033 1.06-2.618c.667-.798 1.6-1.316 2.75-1.811c.255-.11.532-.223.821-.34c.903-.366 1.921-.78 2.753-1.313c1.204-.774 2.21-1.898 2.52-3.706c.363-2.11.042-4.065-1.102-5.517C22.224 14.728 20.405 14 18.25 14c-2.578 0-4.182 1.056-5.115 2.217a5.81 5.81 0 0 0-.885 1.535a5.16 5.16 0 0 0-.224.745l-.004.02l-.001.007v.003l-.001.002c0 .002-.044.125 0 .002a1.25 1.25 0 0 0 1.01 1.45zm1.45-1.001v-.007v.007zm-.002.003l.001-.003v.004zm14.656 5.384a1.25 1.25 0 0 0-1.768 1.768L30.232 29l-2.866 2.866a1.25 1.25 0 0 0 1.768 1.768L32 30.768l2.866 2.866a1.25 1.25 0 0 0 1.768-1.768L33.768 29l2.866-2.866a1.25 1.25 0 0 0-1.768-1.768L32 27.232l-2.866-2.866z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Multiplier2X48Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
