import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 28 28'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M25.78 3.28a.75.75 0 1 0-1.06-1.06l-8.22 8.22V4.25a.75.75 0 0 0-1.5 0v8c0 .414.336.75.75.75h8a.75.75 0 0 0 0-1.5h-6.19l8.22-8.22zM4.091 2.883c1.12-.72 2.553-.995 3.968-.802l.246.033a2.75 2.75 0 0 1 2.088 1.495l1.29 2.582a3.25 3.25 0 0 1-1.103 4.157l-1.708 1.139c-.397.264-.508.631-.42.9c.53 1.614 1.94 3.801 3.169 5.055c.231.235.647.305 1.076.069L14 16.794a3.75 3.75 0 0 1 5.099 1.49l1.042 1.911a2.75 2.75 0 0 1 .014 2.609c-1.45 2.725-4.757 4.238-7.563 2.401c-2.394-1.566-5.336-4.161-7.743-8.33c-2.442-4.23-2.931-7.896-2.78-10.55c.087-1.532.886-2.713 2.021-3.442z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'CallInbound28Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
