'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M336,272c0-29.8-16.3-55.7-40.4-69.5l73.1-124.1C335.6,59.1,297.1,48,256,48c-41.2,0-79.9,11.2-113.1,30.6l71.6,125\r\n\tC191.4,217.6,176,243,176,272H32c0,83.3,46.9,153.4,114.4,192l70.1-122.4c11.7,6.6,25.1,10.4,39.5,10.4c14.3,0,27.7-3.8,39.3-10.3\r\n\tL365.6,464C433.1,425.4,480,355.3,480,272H336z'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'MdNuclear',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
