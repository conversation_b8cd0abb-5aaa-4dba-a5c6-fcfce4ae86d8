import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 16 16'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M8.364 5.346a1.621 1.621 0 0 1 2.293.004c.632.632.637 1.659.004 2.293a1.621 1.621 0 0 1-2.293-.004a1.621 1.621 0 0 1-.004-2.293zm1.585.71a.621.621 0 0 0-.878-.003a.621.621 0 0 0 .004.878a.621.621 0 0 0 .878.004a.621.621 0 0 0-.004-.878zM13.778 3.5a1.947 1.947 0 0 0-1.272-1.272A4.84 4.84 0 0 0 7.63 3.422l-.629.63a2.377 2.377 0 0 0-2.62.502l-.786.785a.5.5 0 0 0 0 .708l.85.85a1.5 1.5 0 0 0 .39 1.441l.167.167l-.773.462a.5.5 0 0 0-.097.783l2.123 2.123a.5.5 0 0 0 .783-.097l.462-.772l.167.166c.39.391.943.521 1.442.39l.85.85a.5.5 0 0 0 .707 0l.785-.785a2.377 2.377 0 0 0 .503-2.62l.629-.63A4.84 4.84 0 0 0 13.778 3.5zm-1.572-.318a.946.946 0 0 1 .618.619a3.84 3.84 0 0 1-.947 3.867l-2.795 2.795a.5.5 0 0 1-.707 0L5.543 7.631a.5.5 0 0 1 0-.707L8.338 4.13a3.84 3.84 0 0 1 3.868-.947zm-1.064 6.635c.037.393-.095.8-.397 1.101l-.432.432l-.352-.352l1.181-1.181zM5.088 5.261a1.374 1.374 0 0 1 1.101-.397l-1.18 1.18l-.353-.351l.432-.432zm1.684 5.014l-.262.437l-1.216-1.216l.438-.262l1.04 1.04zm-1.923 1.59a.5.5 0 1 0-.707-.708l-1.32 1.32a.5.5 0 1 0 .707.708l1.32-1.32zm-1.062-1.769a.5.5 0 0 1 0 .707l-.53.53a.5.5 0 1 1-.706-.708l.53-.529a.5.5 0 0 1 .706 0zm2.123 2.83a.5.5 0 1 0-.707-.707l-.53.53a.5.5 0 1 0 .708.707l.53-.53z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Rocket16Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
