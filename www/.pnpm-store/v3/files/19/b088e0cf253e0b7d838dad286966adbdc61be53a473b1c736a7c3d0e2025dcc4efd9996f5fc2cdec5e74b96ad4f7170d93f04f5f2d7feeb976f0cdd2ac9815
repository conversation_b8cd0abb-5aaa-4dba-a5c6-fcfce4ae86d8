import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10.75 20a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM8.5 19a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zm4.5 0a.75.75 0 1 1 0 1.5a.75.75 0 0 1 0-1.5zM13.001 6.018c3.169 0 4.966 2.097 5.228 4.63h.08A3.687 3.687 0 0 1 22 14.33a3.687 3.687 0 0 1-3.692 3.683H7.694a3.687 3.687 0 0 1-3.692-3.683a3.687 3.687 0 0 1 3.692-3.682h.08c.263-2.55 2.059-4.63 5.227-4.63zM6.588 2.01a5.058 5.058 0 0 1 2.264.674a5.057 5.057 0 0 1 2.208 2.595C9.075 5.84 7.655 7.28 7.066 9.225l-.07.246l-.057.238l-.206.038a4.67 4.67 0 0 0-2.804 1.815l-.155-.085a5.062 5.062 0 0 1-1.642-1.514a.75.75 0 0 1 .366-1.132C4.14 8.243 5.025 7.58 5.53 6.615c.553-1.055.655-2.174.288-3.677a.75.75 0 0 1 .77-.928z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherSnowShowerNight24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
