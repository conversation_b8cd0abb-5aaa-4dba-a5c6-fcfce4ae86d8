'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M29.126 7.125a1.125 1.125 0 1 1 0-2.25H30V2H18v2.875h.875a1.125 1.125 0 0 1 0 2.25H18V10h12V7.125z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M22 13v3H6v-6h9V8H6.186a2.995 2.995 0 0 1 2.816-2h6V4H9a5.006 5.006 0 0 0-5 5v12a4.99 4.99 0 0 0 3.582 4.77L5.77 30h2.176l1.714-4h8.681l1.714 4h2.176l-1.812-4.23A4.99 4.99 0 0 0 24 21v-8zm0 7h-3v2h2.816a2.995 2.995 0 0 1-2.815 2H9a2.995 2.995 0 0 1-2.816-2h2.816v-2H6v-2h16z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = [_hoisted_2, _hoisted_3]
exports.default = (0, vue_1.defineComponent)({
  name: 'TrainTicket',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_4)
  }
})
