import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<path d="M17 28V17h1a2.002 2.002 0 0 0 2-2v-4a2.002 2.002 0 0 0-2-2h-4a2.002 2.002 0 0 0-2 2v4a2.002 2.002 0 0 0 2 2h1v11H2v2h28v-2zm-3-17h4l.002 4H14z" fill="currentColor"></path><path d="M9.332 18.217a7 7 0 0 1 0-10.434l1.334 1.49a5 5 0 0 0 0 7.453z" fill="currentColor"></path><path d="M22.667 18.217l-1.334-1.49a5 5 0 0 0 0-7.454l1.334-1.49a7 7 0 0 1 0 10.434z" fill="currentColor"></path><path d="M6.4 21.8a11.002 11.002 0 0 1 0-17.6l1.2 1.6a9 9 0 0 0 0 14.401z" fill="currentColor"></path><path d="M25.6 21.8l-1.2-1.6a9.001 9.001 0 0 0 0-14.401l1.2-1.6a11.002 11.002 0 0 1 0 17.601z" fill="currentColor"></path>', 5)
const _hoisted_7 = [_hoisted_2]
export default defineComponent({
  name: 'WeatherStation',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_7)
  }
})
