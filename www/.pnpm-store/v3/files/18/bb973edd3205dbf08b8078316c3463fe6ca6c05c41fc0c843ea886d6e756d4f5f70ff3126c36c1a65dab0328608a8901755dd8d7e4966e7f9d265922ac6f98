import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M10 2c2.817 0 4.415 1.923 4.647 4.246h.07C16.532 6.246 18 7.758 18 9.623c0 .046 0 .093-.003.138A3.001 3.001 0 0 0 13.17 11a2.5 2.5 0 0 0-2.32 1.71l-.098.29h-5.47C3.469 13 2 11.488 2 9.623c0-1.865 1.47-3.377 3.282-3.377h.071C5.587 3.908 7.183 2 10 2zm6 12a2 2 0 1 0-2-2h-.78a1.5 1.5 0 0 0-1.422 1.026l-.544 1.632a.5.5 0 0 1-.475.342H9.732A2 2 0 1 0 10 16h.78a1.5 1.5 0 0 0 1.422-1.026l.544-1.632a.5.5 0 0 1 .475-.342h1.047A2 2 0 0 0 16 14zm0-1a1 1 0 1 1 0-2a1 1 0 0 1 0 2zm-7 3a1 1 0 1 1-2 0a1 1 0 0 1 2 0z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'CloudFlow20Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
