'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M4 20v2h4.586L2 28.586L3.414 30L10 23.414V28h2v-8H4z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M30 10l-6-6l-1.414 1.414L27.172 10l-4.586 4.586L24 16l6-6z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_4 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M13.919 17.484L18.069 2L20 2.518L15.85 18z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_5 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M4 10l6-6l1.414 1.414L6.828 10l4.586 4.586L10 16l-6-6z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_6 = [_hoisted_2, _hoisted_3, _hoisted_4, _hoisted_5]
exports.default = (0, vue_1.defineComponent)({
  name: 'CodeReference',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_6)
  }
})
