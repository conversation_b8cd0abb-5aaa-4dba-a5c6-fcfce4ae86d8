import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M34.224 35.992l7.642 7.642a1.25 1.25 0 0 0 1.768-1.768l-37.5-37.5a1.25 1.25 0 1 0-1.768 1.768l6.79 6.79l-5.9 6.421l-.088.105l-.076.11A4.483 4.483 0 0 0 4 22.5v6.992a6.5 6.5 0 0 0 6.5 6.5h4.998a6.5 6.5 0 0 0 6.5-6.5l-.002-4.484h1.244L26 27.767v1.725a6.5 6.5 0 0 0 6.5 6.5h1.724zM16.231 18l-5.665-.001l2.712-2.952L16.232 18zm24.31 17.238L26.268 20.965a4.502 4.502 0 0 1 4.232-2.966l6.954-.001l-4.166-4.515a1.5 1.5 0 0 0-.896-.469L32.185 13h-2.692a1.5 1.5 0 0 1-.203-2.986l.204-.014h2.691a4.5 4.5 0 0 1 3.042 1.184l.265.264l7.284 7.895c.1.109.182.229.244.355c.612.769.978 1.742.978 2.801v6.993a6.5 6.5 0 0 1-3.457 5.745zM15.33 10.027L18.303 13h.196l.203-.014A1.5 1.5 0 0 0 18.5 10h-2.681l-.376.016a4.2 4.2 0 0 0-.112.01z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'GlassesOff48Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
