'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M10 2a8 8 0 1 0 0 16a8 8 0 0 0 0-16zM7.092 3.63l2.41 1.406v1.7L6.733 8.688l-1.56-.567l-.626-2.512A7.025 7.025 0 0 1 7.092 3.63zm-4.058 7.062l1.827-1.62l1.546.561l1.045 3.29l-.717 1.073H4.252a6.96 6.96 0 0 1-1.218-3.304zm5.339 6.118l-.815-2.246l.71-1.063h3.467l.711 1.063l-.815 2.245a7.015 7.015 0 0 1-3.258 0zm7.375-2.814H13.27l-.72-1.075l1.025-3.287l1.567-.564l1.824 1.622a6.96 6.96 0 0 1-1.218 3.304zm-.29-8.38l-.626 2.503l-1.584.57l-2.747-1.952v-1.7L12.91 3.63a7.024 7.024 0 0 1 2.548 1.985z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'SportSoccer20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
