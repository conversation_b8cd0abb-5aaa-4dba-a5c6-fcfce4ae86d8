import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  x: '0px',
  y: '0px',
  viewBox: '0 0 512 512',
  'enable-background': 'new 0 0 512 512',
  'xml:space': 'preserve'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  null,
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M227.8,300.2c-5.1-5.1-5.1-13.3,0-18.4l133.7-133.7c-5.3-2.6-11.2-4.1-17.5-4.1H88c-22,0-40,18-40,40v224c0,22,18,40,40,40\n\t\th256c22,0,40-18,40-40V184c0-6.3-1.5-12.2-4.1-17.5L246.2,300.2C241.1,305.3,232.9,305.3,227.8,300.2z'
    }),
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M459.5,68.5C457,66,453,64,449,64c-5,0-97,0-97,0c-7.2-0.1-13.1,5.7-13.1,12.9c-0.1,7.2,5.7,13.1,12.9,13.1l67.3,0.5\n\t\tl-57.6,57.6c8,3.9,14.5,10.4,18.4,18.4l57.6-57.6l0.5,67.3c0.1,7.2,5.9,13,13.1,12.9c7.2-0.1,13-5.9,12.9-13.1l0-98\n\t\tC464,74.5,462,71,459.5,68.5z'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'IosOpen',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
