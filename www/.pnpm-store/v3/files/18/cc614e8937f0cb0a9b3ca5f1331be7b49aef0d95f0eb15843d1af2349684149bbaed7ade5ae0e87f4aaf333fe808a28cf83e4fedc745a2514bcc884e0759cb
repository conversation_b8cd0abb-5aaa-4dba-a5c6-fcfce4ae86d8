import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<path d="M5 25v-9.172l-3.586 3.586L0 18l6-6l6 6l-1.414 1.414L7 15.828V25h12v2H7a2.002 2.002 0 0 1-2-2z" fill="currentColor"></path><path d="M24 22h4a2.002 2.002 0 0 1 2 2v4a2.002 2.002 0 0 1-2 2h-4a2.002 2.002 0 0 1-2-2v-4a2.002 2.002 0 0 1 2-2zm4 6v-4h-4.002L24 28z" fill="currentColor"></path><path d="M27 6v9.172l3.586-3.586L32 13l-6 6l-6-6l1.414-1.414L25 15.172V6H13V4h12a2.002 2.002 0 0 1 2 2z" fill="currentColor"></path><path d="M2 6h6v2H2z" fill="currentColor"></path><path d="M2 2h8v2H2z" fill="currentColor"></path>', 5)
const _hoisted_7 = [_hoisted_2]
export default defineComponent({
  name: 'DataShare',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_7)
  }
})
