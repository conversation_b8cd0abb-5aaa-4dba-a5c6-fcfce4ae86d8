'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M5.703 8.748a.75.75 0 0 0 .799-.701a1.027 1.027 0 0 1 .148-.388c.109-.168.338-.409.952-.409c.58 0 .861.186 1.003.38c.158.216.266.61.157 1.243a.78.78 0 0 1-.402.597c-.204.126-.423.206-.719.314c-.127.047-.269.099-.43.162c-.474.185-1.057.456-1.504.997c-.461.558-.704 1.308-.704 2.307a.75.75 0 0 0 .75.75H9.5a.75.75 0 0 0 0-1.5H6.58c.069-.285.175-.47.283-.6c.195-.238.478-.395.894-.557a12.6 12.6 0 0 1 .27-.101c.343-.125.776-.282 1.123-.497c.523-.324.954-.82 1.09-1.618c.15-.867.057-1.723-.424-2.382c-.497-.68-1.294-.995-2.214-.995c-1.135 0-1.831.51-2.21 1.091a2.527 2.527 0 0 0-.386 1.09l-.001.011v.006c0 .003-.01.143 0 .002a.75.75 0 0 0 .698.798zm6.578 1.472a.75.75 0 1 0-1.06 1.06l.72.72l-.72.72a.75.75 0 0 0 1.06 1.06l.72-.72l.719.72a.75.75 0 1 0 1.06-1.06l-.72-.72l.72-.72a.75.75 0 0 0-1.06-1.06l-.72.72l-.719-.72z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'Multiplier2X20Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
