import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M2 10.5a7.5 7.5 0 1 1 4.411 6.836c-1.258.29-2.613.54-3.236.652a.996.996 0 0 1-1.153-1.17c.118-.61.38-1.918.681-3.143A7.474 7.474 0 0 1 2 10.5zm6.144-4.193c-.442.236-.79.603-1.053 1.09a.75.75 0 0 0 1.318.714c.147-.27.298-.404.442-.48c.15-.081.354-.13.649-.13c.224 0 .487.079.68.238c.171.14.32.364.32.761c0 .195-.075.285-.588.688c-.45.354-1.162.928-1.162 2.062a.75.75 0 0 0 1.5 0c0-.37.164-.549.588-.883l.085-.065C11.31 10.004 12 9.475 12 8.5c0-.848-.351-1.498-.868-1.922A2.614 2.614 0 0 0 9.5 6.001c-.455 0-.922.074-1.356.306zM10.5 14a1 1 0 1 0-2 0a1 1 0 0 0 2 0zm-1.1 5a7.474 7.474 0 0 0 5.1 2c1.1 0 2.146-.237 3.089-.664c1.26.29 2.621.54 3.248.65a.996.996 0 0 0 1.15-1.175a74.514 74.514 0 0 0-.69-3.136A7.503 7.503 0 0 0 16.954 6.41A8.5 8.5 0 0 1 9.4 19z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'ChatBubblesQuestion24Filled',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
