'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'path',
  {
    d: 'M1 13c0-1.1.9-2 2-2s2 .9 2 2s-.9 2-2 2s-2-.9-2-2zm7.89-2.89l4.53-1.21l-.78-2.9l-4.53 1.21c-.8.21-1.28 1.04-1.06 1.84c.22.8 1.04 1.28 1.84 1.06zM20.5 5.9L23 3l-1-1l-3 3l-2 4l-9.48 2.87c-.82.2-1.39.89-1.5 1.68L5.24 18L2.4 21.8L4 23l3-4l1.14-3.14L14 14l5-3.5l1.5-4.6z',
    fill: 'currentColor'
  },
  null,
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ScubaDivingFilled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
