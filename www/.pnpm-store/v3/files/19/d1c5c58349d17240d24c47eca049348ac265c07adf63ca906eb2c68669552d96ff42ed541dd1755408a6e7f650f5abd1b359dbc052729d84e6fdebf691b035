'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 20 20'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M3 4.5a.5.5 0 0 0-1 0v11a.5.5 0 0 0 1 0V10h6v5.5a.5.5 0 0 0 1 0v-11a.5.5 0 0 0-1 0V9H3V4.5zm8.98 2.139c.086-.296.283-.705.664-1.037c.372-.324.95-.602 1.856-.602C16.236 5 17 6.188 17 7c0 .496-.102 1.113-.494 1.599c-.375.465-1.088.901-2.506.901a.5.5 0 0 0 0 1c.507 0 1.294.062 1.938.36c.315.146.576.34.759.593c.179.248.303.58.303 1.047c0 1.005-.33 1.602-.758 1.96c-.444.37-1.066.54-1.742.54c-.7 0-1.22-.118-1.61-.361c-.38-.238-.693-.63-.916-1.297a.5.5 0 1 0-.948.316c.277.833.714 1.44 1.334 1.829c.61.38 1.34.513 2.14.513c.824 0 1.702-.205 2.383-.772c.696-.58 1.117-1.483 1.117-2.728c0-.659-.18-1.2-.492-1.632a2.868 2.868 0 0 0-1.113-.898c.364-.203.658-.456.89-.744C17.88 8.487 18 7.604 18 7c0-1.388-1.236-3-3.5-3c-1.13 0-1.947.355-2.512.848a3.216 3.216 0 0 0-.968 1.513a.5.5 0 0 0 .96.278z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'TextHeader320Regular',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
