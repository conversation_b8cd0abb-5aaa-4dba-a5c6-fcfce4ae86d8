'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ (0, vue_1.createElementVNode)(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ (0, vue_1.createElementVNode)('path', {
      d: 'M27 16c0-6.075-4.925-11-11-11c-2.923 0-5.58 1.14-7.55 3h2.3a1.25 1.25 0 1 1 0 2.5h-4.5C5.56 10.5 5 9.94 5 9.25v-4.5a1.25 1.25 0 1 1 2.5 0v.761A13.444 13.444 0 0 1 16 2.5c7.456 0 13.5 6.044 13.5 13.5S23.456 29.5 16 29.5S2.5 23.456 2.5 16c0-.28.009-.558.025-.833C2.566 14.494 3.146 14 3.82 14c.709 0 1.24.643 1.199 1.35c-.013.215-.019.432-.019.65c0 6.075 4.925 11 11 11s11-4.925 11-11z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
exports.default = (0, vue_1.defineComponent)({
  name: 'ArrowCounterclockwise32Filled',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _hoisted_3)
  }
})
