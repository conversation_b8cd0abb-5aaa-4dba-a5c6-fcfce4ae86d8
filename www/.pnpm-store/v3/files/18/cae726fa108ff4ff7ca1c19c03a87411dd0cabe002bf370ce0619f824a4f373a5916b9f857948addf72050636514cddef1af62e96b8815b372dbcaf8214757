import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
const _hoisted_2 = /*#__PURE__*/ _createElementVNode(
  'g',
  {
    fill: 'none'
  },
  [
    /*#__PURE__*/ _createElementVNode('path', {
      d: 'M9.703 2.265A10.026 10.026 0 0 1 12 2c.79 0 1.559.092 2.297.265a.75.75 0 1 1-.343 1.46A8.527 8.527 0 0 0 12 3.5c-.673 0-1.327.078-1.954.225a.75.75 0 1 1-.343-1.46zm-1.93 1.47a.75.75 0 0 1-.242 1.033a8.548 8.548 0 0 0-2.763 2.763a.75.75 0 1 1-1.275-.79a10.048 10.048 0 0 1 3.248-3.248a.75.75 0 0 1 1.032.243zm8.454 0a.75.75 0 0 1 1.032-.242a10.048 10.048 0 0 1 3.248 3.248a.75.75 0 1 1-1.275.79a8.55 8.55 0 0 0-2.763-2.763a.75.75 0 0 1-.242-1.032zm-13.06 5.41a.75.75 0 0 1 .558.901A8.527 8.527 0 0 0 3.5 12c0 .673.078 1.327.225 1.954a.75.75 0 1 1-1.46.343A10.026 10.026 0 0 1 2 12c0-.79.092-1.559.265-2.297a.75.75 0 0 1 .902-.559zm17.666 0a.75.75 0 0 1 .902.558C21.908 10.44 22 11.21 22 12c0 .79-.092 1.559-.265 2.297a.75.75 0 1 1-1.46-.343c.147-.627.225-1.28.225-1.954c0-.673-.078-1.327-.226-1.954a.75.75 0 0 1 .559-.902zM3.736 16.226a.75.75 0 0 1 1.032.242a8.548 8.548 0 0 0 2.763 2.763a.75.75 0 0 1-.79 1.275a10.048 10.048 0 0 1-3.248-3.248a.75.75 0 0 1 .243-1.032zm16.685.858a.75.75 0 1 0-1.342-.67l-.002.004l-.015.029a8.004 8.004 0 0 1-.358.59a9.58 9.58 0 0 1-.965 1.218c-1.17-1.073-2.756-2.006-4.74-2.006c-2.347 0-3.99 1.203-3.99 2.875S10.653 22 13 22c1.942 0 3.495-.75 4.658-1.645a11.73 11.73 0 0 1 1.315 2.01c.033.065.058.116.073.149l.017.035l.004.009a.75.75 0 0 0 1.368-.615c-.087-.183 0-.001 0-.001v-.002l-.003-.004l-.007-.015a11.874 11.874 0 0 0-.464-.87a13.199 13.199 0 0 0-1.189-1.703a11.057 11.057 0 0 0 1.525-2.032l.09-.162l.024-.047l.007-.014l.002-.005l.002-.003zM13 17.75c1.433 0 2.644.652 3.616 1.512c-.95.7-2.155 1.238-3.616 1.238c-1.973 0-2.49-.922-2.49-1.375c0-.453.517-1.375 2.49-1.375z',
      fill: 'currentColor'
    })
  ],
  -1
  /* HOISTED */
)
const _hoisted_3 = [_hoisted_2]
export default defineComponent({
  name: 'Lasso24Regular',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_3)
  }
})
