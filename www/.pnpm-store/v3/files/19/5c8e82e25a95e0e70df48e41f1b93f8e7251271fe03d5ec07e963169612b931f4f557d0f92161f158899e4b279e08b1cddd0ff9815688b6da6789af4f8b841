import { openBlock as _openBlock, createElementBlock as _createElementBlock, createStaticVNode as _createStaticVNode, defineComponent } from 'vue'
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 32 32'
}
const _hoisted_2 = /*#__PURE__*/ _createStaticVNode('<path d="M12 6h14v2H12z" fill="currentColor"></path><path d="M12 12h10v2H12z" fill="currentColor"></path><path d="M12 18h14v2H12z" fill="currentColor"></path><path d="M12 24h10v2H12z" fill="currentColor"></path><path d="M6 4h2v24H6z" fill="currentColor"></path>', 5)
const _hoisted_7 = [_hoisted_2]
export default defineComponent({
  name: 'TextAlignLeft',
  render: function render(_ctx, _cache) {
    return _openBlock(), _createElementBlock('svg', _hoisted_1, _hoisted_7)
  }
})
